<?php

namespace app\applent\logic\article;

use app\common\enum\YesNoEnum;
use app\common\logic\BaseLogic;
use app\common\model\article\Article;

/**
 * 文章逻辑
 * Class ArticleLogic
 * @package app\applent\logic\article
 */
class ArticleLogic extends BaseLogic
{
    /**
     * @notes 文章详情
     * @param int $articleId 文章ID
     * @param int $userId 用户ID
     * @return array|false
     * <AUTHOR>
     * @date 2025-01-08
     */
    public static function detail(int $articleId, int $userId = 0)
    {
        try {
            // 查询文章详情
            $article = Article::where([
                'id' => $articleId,
                'is_show' => YesNoEnum::YES
            ])->findOrEmpty();

            if ($article->isEmpty()) {
                self::setError('文章不存在或已下架');
                return false;
            }

            // 增加点击量
            $article->click_actual += 1;
            $article->save();

            // 返回数据
            $result = [
                'id' => $article->id,
                'title' => $article->title,
                'desc' => $article->desc,
                'image' => $article->image,
                'content' => $article->content,
                'sort' => $article->sort,
                'is_show' => $article->is_show,
                'create_time' => date('Y-m-d H:i:s', $article->create_time),
                'click' => ($article->click_actual + $article->click_virtual)
            ];

            return $result;
            
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 获取文章分类列表
     * @return array
     * <AUTHOR>
     * @date 2025-01-08
     */
    public static function getCateList()
    {
        try {
            $cateList = \app\common\model\article\ArticleCate::where([
                'is_show' => YesNoEnum::YES
            ])
            ->field('id,name,sort')
            ->order('sort desc, id desc')
            ->select()
            ->toArray();

            return $cateList;
            
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return [];
        }
    }
}
