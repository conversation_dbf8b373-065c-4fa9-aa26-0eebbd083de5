<?php

namespace app\applent\controller\login;

use app\applent\controller\BaseApiController;
use app\applent\logic\article\ArticleLogic;

/**
 * 文章详情控制器
 * Class ArticleController
 * @package app\applent\controller\login
 */
class ArticleController extends BaseApiController
{
    public array $notNeedLogin = ['detail'];

    /**
     * @notes 文章详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025-01-08
     */
    public function detail()
    {
        $id = $this->request->get('id/d');
        if (empty($id)) {
            return $this->fail('文章ID不能为空');
        }
        
        $result = ArticleLogic::detail($id, $this->userId);
        if (false === $result) {
            return $this->fail(ArticleLogic::getError());
        }
        
        return $this->success('获取成功', $result, 1, 0);
    }
}
