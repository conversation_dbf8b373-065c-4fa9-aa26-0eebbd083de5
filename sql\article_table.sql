-- 文章表结构
CREATE TABLE IF NOT EXISTS `la_article` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '文章id',
  `cid` int(11) NOT NULL DEFAULT 0 COMMENT '文章分类',
  `title` varchar(255) NOT NULL COMMENT '文章标题',
  `desc` varchar(255) NULL DEFAULT '' COMMENT '文章简介',
  `abstract` text NULL COMMENT '文章摘要',
  `image` varchar(128) NULL DEFAULT NULL COMMENT '文章封面（单张）',
  `author` varchar(255) NULL DEFAULT '' COMMENT '作者',
  `content` text NULL COMMENT '文章内容',
  `click_virtual` int(10) NULL DEFAULT 0 COMMENT '虚拟浏览量',
  `click_actual` int(11) NULL DEFAULT 0 COMMENT '实际浏览量',
  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示:1-是.0-否',
  `sort` int(5) NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '发布时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(11) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_cid` (`cid`),
  KEY `idx_is_show` (`is_show`),
  KEY `idx_sort` (`sort`),
  KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT = '文章表' ROW_FORMAT = Dynamic;

-- 文章分类表结构
CREATE TABLE IF NOT EXISTS `la_article_cate` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '文章分类id',
  `name` varchar(90) NULL DEFAULT NULL COMMENT '分类名称',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_show` tinyint(1) NULL DEFAULT 1 COMMENT '是否显示:1-是;0-否',
  `create_time` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `delete_time` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_is_show` (`is_show`),
  KEY `idx_sort` (`sort`)
) ENGINE = InnoDB COMMENT = '文章分类表' ROW_FORMAT = Dynamic;

-- 插入示例数据
INSERT INTO `la_article_cate` (`id`, `name`, `sort`, `is_show`, `create_time`, `update_time`) VALUES
(1, '公司新闻', 100, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '行业资讯', 90, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '技术分享', 80, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

INSERT INTO `la_article` (`id`, `cid`, `title`, `desc`, `image`, `content`, `is_show`, `sort`, `create_time`, `update_time`) VALUES
(1, 1, '欢迎使用文章系统', '这是一篇示例文章，展示文章系统的基本功能', '', '<p>这是文章的详细内容，支持HTML格式。</p><p>您可以在这里编写丰富的文章内容。</p>', 1, 100, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 2, '系统功能介绍', '详细介绍系统的各项功能特性', '', '<p>本系统提供完整的文章管理功能：</p><ul><li>文章列表展示</li><li>文章详情查看</li><li>分类管理</li><li>排序功能</li></ul>', 1, 90, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
