<?php

namespace app\applent\lists\article;

use app\applent\lists\BaseApiDataLists;
use app\common\enum\YesNoEnum;
use app\common\lists\ListsSearchInterface;
use app\common\model\article\Article;

/**
 * 文章列表
 * Class ArticleLists
 * @package app\applent\lists\article
 */
class ArticleLists extends BaseApiDataLists implements ListsSearchInterface
{
    /**
     * @notes 搜索条件
     * @return array
     * <AUTHOR>
     * @date 2025-01-08
     */
    public function setSearch(): array
    {
        return [
            '=' => ['cid']
        ];
    }

    /**
     * @notes 自定义查询条件
     * @return array
     * <AUTHOR>
     * @date 2025-01-08
     */
    public function queryWhere()
    {
        $where[] = ['is_show', '=', YesNoEnum::YES];
        
        // 关键词搜索
        if (!empty($this->params['keyword'])) {
            $where[] = ['title', 'like', '%' . $this->params['keyword'] . '%'];
        }
        
        return $where;
    }

    /**
     * @notes 获取文章列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2025-01-08
     */
    public function lists(): array
    {
        // 排序规则
        $orderRaw = 'sort desc, id desc';
        $sortType = $this->params['sort'] ?? 'default';
        
        // 最新排序
        if ($sortType == 'new') {
            $orderRaw = 'create_time desc, id desc';
        }
        
        // 最热排序
        if ($sortType == 'hot') {
            $orderRaw = 'click_actual + click_virtual desc, id desc';
        }

        // 查询字段
        $field = 'id,title,desc,image,sort,is_show,create_time';
        
        $result = Article::field($field)
            ->where($this->queryWhere())
            ->where($this->searchWhere)
            ->orderRaw($orderRaw)
            ->limit($this->limitOffset, $this->limitLength)
            ->select()
            ->toArray();

        // 格式化数据
        foreach ($result as &$item) {
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
        }

        return $result;
    }

    /**
     * @notes 获取数量
     * @return int
     * <AUTHOR>
     * @date 2025-01-08
     */
    public function count(): int
    {
        return Article::where($this->queryWhere())
            ->where($this->searchWhere)
            ->count();
    }
}
