<?php

namespace app\applent\controller\list;

use app\applent\controller\BaseApiController;
use app\applent\lists\article\ArticleLists;

/**
 * 文章列表控制器
 * Class ArticleController
 * @package app\applent\controller\list
 */
class ArticleController extends BaseApiController
{
    public array $notNeedLogin = ['lists'];

    /**
     * @notes 文章列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025-01-08
     */
    public function lists()
    {
        return $this->dataLists(new ArticleLists());
    }
}
